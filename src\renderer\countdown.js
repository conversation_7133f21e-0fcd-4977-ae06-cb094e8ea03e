class CountdownManager {
    constructor() {
        this.totalTime = 5 * 60; // 5分钟，单位：秒
        this.remainingTime = this.totalTime;
        this.isRunning = true;
        this.isCancelled = false;

        // DOM 元素
        this.countdownTimeEl = document.getElementById('countdownTime');
        this.progressCircleEl = document.getElementById('progressCircle');
        this.progressPercentageEl = document.getElementById('progressPercentage');
        this.slideButtonEl = document.getElementById('slideButton');
        this.slideProgressEl = document.getElementById('slideProgress');
        this.slideTextEl = document.getElementById('slideText');
        this.statusMessageEl = document.getElementById('statusMessage');
        this.containerEl = document.querySelector('.countdown-container');
        this.holdPromptEl = document.getElementById('holdPrompt');
        this.holdTimerEl = document.getElementById('holdTimer');
        this.holdProgressBarEl = document.getElementById('holdProgressBar');

        // 滑动相关变量
        this.isDragging = false;
        this.startX = 0;
        this.currentX = 0;
        this.slideTrackWidth = 0;
        this.buttonWidth = 52;
        this.maxSlideDistance = 0;
        this.holdTimer = null;
        this.holdDuration = 1000; // 1秒
        this.isHolding = false;
        this.holdStartTime = 0;
        this.holdAnimationFrame = null;

        // 进度环配置
        this.circleRadius = 90;
        this.circleCircumference = 2 * Math.PI * this.circleRadius;

        this.init();
    }

    init() {
        this.setupProgressRing();
        this.setupSlideToCancel();
        this.startCountdown();
        this.updateDisplay();
    }

    setupProgressRing() {
        this.progressCircleEl.style.strokeDasharray = this.circleCircumference;
        this.progressCircleEl.style.strokeDashoffset = 0;
    }

    setupSlideToCancel() {
        const slideTrack = document.querySelector('.slide-track');
        this.slideTrackWidth = slideTrack.offsetWidth;
        this.maxSlideDistance = this.slideTrackWidth - this.buttonWidth - 8; // 8px for padding

        // 初始化按钮位置（从右边开始）
        this.currentX = 0; // 相对于右边的偏移
        this.slideButtonEl.style.transform = 'translateX(0px)';

        // 鼠标事件
        this.slideButtonEl.addEventListener('mousedown', this.handleStart.bind(this));
        document.addEventListener('mousemove', this.handleMove.bind(this));
        document.addEventListener('mouseup', this.handleEnd.bind(this));

        // 触摸事件
        this.slideButtonEl.addEventListener('touchstart', this.handleStart.bind(this));
        document.addEventListener('touchmove', this.handleMove.bind(this));
        document.addEventListener('touchend', this.handleEnd.bind(this));

        // 防止默认拖拽行为
        this.slideButtonEl.addEventListener('dragstart', (e) => e.preventDefault());
    }

    handleStart(e) {
        if (this.isCancelled) return;

        e.preventDefault();
        this.isDragging = true;
        this.isHolding = false;

        const clientX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
        const buttonRect = this.slideButtonEl.getBoundingClientRect();
        this.startX = clientX - buttonRect.left;

        this.slideButtonEl.classList.add('dragging');
        this.addRippleEffect();

        // 清除之前的定时器
        if (this.holdTimer) {
            clearTimeout(this.holdTimer);
        }
    }

    handleMove(e) {
        if (!this.isDragging || this.isCancelled) return;

        e.preventDefault();
        const clientX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
        const slideTrack = document.querySelector('.slide-track');
        const trackRect = slideTrack.getBoundingClientRect();

        // 计算相对于滑动轨道左边的位置
        const relativeX = clientX - trackRect.left - this.buttonWidth/2;
        this.currentX = Math.max(Math.min(relativeX, this.maxSlideDistance), 0);

        // 更新按钮位置（从右边开始，向左滑动）
        this.slideButtonEl.style.transform = `translateX(${this.currentX - this.maxSlideDistance}px)`;

        // 更新进度条（从右边开始填充）
        const progress = this.currentX / this.maxSlideDistance;
        this.slideProgressEl.style.width = `${progress * 100}%`;

        // 用户拖动时使用绿色
        this.slideProgressEl.classList.remove('auto-slide');
        this.slideProgressEl.classList.add('user-drag');

        // 更新文本透明度
        this.slideTextEl.style.opacity = 1 - progress * 0.7;

        // 检查是否滑动到足够距离（向左滑动）
        if (this.currentX >= this.maxSlideDistance * 0.85) {
            if (!this.isHolding) {
                this.startHoldTimer();
            }
        } else {
            this.stopHoldTimer();
        }
    }

    handleEnd() {
        if (!this.isDragging || this.isCancelled) return;

        this.isDragging = false;
        this.slideButtonEl.classList.remove('dragging');

        // 如果没有完成取消操作，回弹到当前自动滑动位置
        if (!this.isHolding || this.currentX < this.maxSlideDistance * 0.8) {
            this.bounceBack();
        }

        this.stopHoldTimer();
    }

    startHoldTimer() {
        this.isHolding = true;
        this.holdStartTime = performance.now();

        // 显示大屏幕提示
        this.holdPromptEl.classList.add('show');

        // 添加保持动画效果
        this.slideButtonEl.classList.add('holding');
        this.slideProgressEl.classList.add('holding');
        document.querySelector('.slide-track').classList.add('holding');
        this.slideTextEl.classList.add('holding');
        this.slideTextEl.textContent = '继续保持以取消关机...';

        // 开始倒计时动画
        this.updateHoldProgress();

        this.holdTimer = setTimeout(() => {
            if (this.isHolding && this.currentX >= this.maxSlideDistance * 0.8) {
                this.cancelShutdown();
            }
        }, this.holdDuration);
    }

    stopHoldTimer() {
        this.isHolding = false;

        // 隐藏大屏幕提示
        this.holdPromptEl.classList.remove('show');

        // 停止倒计时动画
        if (this.holdAnimationFrame) {
            cancelAnimationFrame(this.holdAnimationFrame);
            this.holdAnimationFrame = null;
        }

        // 移除保持动画效果
        this.slideButtonEl.classList.remove('holding');
        this.slideProgressEl.classList.remove('holding');
        document.querySelector('.slide-track').classList.remove('holding');
        this.slideTextEl.classList.remove('holding');
        this.slideTextEl.textContent = '向左滑动取消关机';

        if (this.holdTimer) {
            clearTimeout(this.holdTimer);
            this.holdTimer = null;
        }
    }

    updateHoldProgress() {
        if (!this.isHolding) return;

        const elapsed = performance.now() - this.holdStartTime;
        const progress = Math.min(elapsed / this.holdDuration, 1);
        const remaining = Math.max(this.holdDuration - elapsed, 0) / 1000;

        // 更新倒计时显示
        this.holdTimerEl.textContent = remaining.toFixed(1);

        // 更新进度条
        this.holdProgressBarEl.style.width = `${progress * 100}%`;

        if (progress < 1) {
            this.holdAnimationFrame = requestAnimationFrame(() => this.updateHoldProgress());
        }
    }

    bounceBack() {
        // 计算当前应该的自动滑动位置
        const autoSlideProgress = (this.totalTime - this.remainingTime) / this.totalTime;
        const autoSlideDistance = autoSlideProgress * this.maxSlideDistance;

        this.slideButtonEl.classList.add('bounce-back');
        this.slideButtonEl.style.setProperty('--drag-distance', `${this.currentX - this.maxSlideDistance}px`);
        this.slideButtonEl.style.setProperty('--target-distance', `${autoSlideDistance - this.maxSlideDistance}px`);

        // 动画结束后更新位置
        setTimeout(() => {
            this.slideButtonEl.style.transform = `translateX(${autoSlideDistance - this.maxSlideDistance}px)`;
            this.slideProgressEl.style.width = `${autoSlideProgress * 100}%`;
            this.slideProgressEl.classList.remove('user-drag', 'holding');
            this.slideProgressEl.classList.add('auto-slide');
            this.slideTextEl.style.opacity = 1 - autoSlideProgress * 0.7;
            this.currentX = autoSlideDistance;
            this.slideButtonEl.classList.remove('bounce-back');
        }, 500);
    }

    addRippleEffect() {
        const ripple = this.slideButtonEl.querySelector('.slide-button-ripple');
        ripple.classList.remove('active');
        setTimeout(() => ripple.classList.add('active'), 10);
    }

    cancelShutdown() {
        this.isCancelled = true;
        this.isRunning = false;

        // 隐藏大屏幕提示
        this.holdPromptEl.classList.remove('show');

        // 显示成功动画
        this.slideButtonEl.classList.add('success');
        this.slideProgressEl.style.width = '100%';
        this.slideProgressEl.style.background = 'linear-gradient(90deg, #4CAF50, #45a049)';

        // 显示状态消息，保持1秒
        this.showStatusMessage('关机已取消', 'success');

        // 通知主进程
        if (window.electronAPI) {
            window.electronAPI.cancelShutdown();
        }

        // 1秒后直接关闭窗口
        setTimeout(() => {
            if (window.electronAPI) {
                window.electronAPI.closeWindow();
            }
        }, 1000);
    }

    startCountdown() {
        const timer = setInterval(() => {
            if (!this.isRunning || this.isCancelled) {
                clearInterval(timer);
                return;
            }

            this.remainingTime--;
            this.updateDisplay();

            // 检查是否到时间
            if (this.remainingTime <= 0) {
                clearInterval(timer);
                this.executeShutdown();
            }

            // 最后30秒进入紧急状态
            if (this.remainingTime <= 30 && !this.containerEl.classList.contains('urgent')) {
                this.containerEl.classList.add('urgent');
                this.showStatusMessage('警告：系统即将关机！', 'warning');
            }
        }, 1000);
    }

    updateDisplay() {
        // 更新倒计时显示
        const minutes = Math.floor(this.remainingTime / 60);
        const seconds = this.remainingTime % 60;
        this.countdownTimeEl.textContent =
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        // 更新进度环
        const progress = (this.totalTime - this.remainingTime) / this.totalTime;
        const offset = this.circleCircumference * (1 - progress);
        this.progressCircleEl.style.strokeDashoffset = offset;

        // 更新百分比
        const percentage = Math.round((this.remainingTime / this.totalTime) * 100);
        this.progressPercentageEl.textContent = `${percentage}%`;

        // 自动滑动效果（随时间推移从右向左）
        if (!this.isDragging && !this.isCancelled) {
            const autoSlideProgress = (this.totalTime - this.remainingTime) / this.totalTime;
            const autoSlideDistance = autoSlideProgress * this.maxSlideDistance;

            // 更新按钮位置（从右边开始，向左移动）
            this.slideButtonEl.style.transform = `translateX(${autoSlideDistance - this.maxSlideDistance}px)`;
            this.currentX = autoSlideDistance;

            // 更新进度条 - 自动滑动时使用白色半透明
            this.slideProgressEl.style.width = `${autoSlideProgress * 100}%`;
            this.slideProgressEl.classList.remove('user-drag', 'holding');
            this.slideProgressEl.classList.add('auto-slide');

            // 更新文本透明度
            this.slideTextEl.style.opacity = 1 - autoSlideProgress * 0.7;
        }
    }

    executeShutdown() {
        this.showStatusMessage('正在关机...', 'info');

        // 通知主进程执行关机
        if (window.electronAPI) {
            window.electronAPI.executeShutdown();
        }
    }

    showStatusMessage(message, type = 'info') {
        this.statusMessageEl.textContent = message;
        this.statusMessageEl.className = `status-message ${type}`;
        this.statusMessageEl.classList.add('show');

        // 根据类型设置隐藏时间
        let hideDelay = 3000; // 默认3秒
        if (type === 'success') {
            hideDelay = 1000; // 成功消息1秒
        }

        setTimeout(() => {
            this.statusMessageEl.classList.remove('show');
        }, hideDelay);
    }
}

// 工具函数
class AnimationUtils {
    static easeOutBounce(t) {
        if (t < 1 / 2.75) {
            return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
            return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
            return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
    }

    static animate(element, property, from, to, duration, easing = 'ease') {
        const start = performance.now();

        function update(currentTime) {
            const elapsed = currentTime - start;
            const progress = Math.min(elapsed / duration, 1);

            let easedProgress = progress;
            if (easing === 'bounce') {
                easedProgress = AnimationUtils.easeOutBounce(progress);
            }

            const value = from + (to - from) * easedProgress;
            element.style[property] = `${value}px`;

            if (progress < 1) {
                requestAnimationFrame(update);
            }
        }

        requestAnimationFrame(update);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    // 防止右键菜单
    document.addEventListener('contextmenu', (e) => {
        e.preventDefault();
    });

    // 防止选择文本
    document.addEventListener('selectstart', (e) => {
        e.preventDefault();
    });
});