# 项目结构说明

## 文件夹结构

```
src/
├── renderer/           # 渲染进程文件
│   ├── index.html     # 主界面 HTML
│   ├── app.js         # 主应用逻辑
│   ├── countdown.html # 倒计时界面
│   └── countdown.js   # 倒计时逻辑
├── components/         # UI 组件
│   ├── UIManager.js   # 界面管理器，负责更新计划列表显示
│   └── ModalManager.js # 模态框管理器，负责弹窗操作
├── services/          # 业务逻辑服务
│   └── ScheduleService.js # 计划服务，负责计划的增删改查和与主进程通信
├── utils/             # 工具函数
│   └── timeUtils.js   # 时间相关工具函数
├── config/            # 配置文件
│   └── constants.js   # 常量定义
└── styles/            # 样式文件
    └── main.css       # 主样式文件
```

## 模块说明

### ScheduleService
- 管理定时关机计划数据
- 与 Electron 主进程通信
- 提供计划的增删改查功能

### UIManager
- 负责更新界面显示
- 渲染计划列表
- 处理界面交互

### ModalManager
- 管理所有模态框
- 处理表单重置
- 显示提示信息

### timeUtils
- 时间格式化工具
- 时间解析工具
- 星期显示格式化

### constants
- 统一管理常量
- 消息文本
- DOM 选择器
- 配置项

## 重构优势

1. **模块化**: 代码按功能分离，便于维护
2. **可复用**: 工具函数可在多处使用
3. **易测试**: 每个模块职责单一，便于单元测试
4. **可扩展**: 新功能可以独立添加模块
5. **易维护**: 修改某个功能只需要关注对应模块
