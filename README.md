# 定时关机程序

这是一个基于 Electron 的定时关机程序，采用模块化架构设计，代码结构清晰，易于维护和扩展。

## 项目结构

```
electron/
├── main.js              # Electron 主进程
├── preload.js           # 预加载脚本
├── config.json          # 配置文件
├── package.json         # 项目配置
└── src/                 # 源代码目录
    ├── renderer/        # 渲染进程文件
    │   ├── index.html   # 主界面
    │   ├── app.js       # 主应用逻辑
    │   ├── countdown.html # 倒计时界面
    │   └── countdown.js # 倒计时逻辑
    ├── components/      # UI 组件
    │   ├── UIManager.js # 界面管理器
    │   └── ModalManager.js # 模态框管理器
    ├── services/        # 业务逻辑服务
    │   └── ScheduleService.js # 计划服务
    ├── utils/           # 工具函数
    │   └── timeUtils.js # 时间工具
    ├── config/          # 配置管理
    │   └── constants.js # 常量定义
    └── styles/          # 样式文件
        └── main.css     # 主样式
```

## 功能特性

- ✅ 设置多个关机时间计划
- ✅ 选择特定的星期几执行
- ✅ 系统托盘运行，支持后台监听
- ✅ 全屏倒计时提醒
- ✅ **配置持久化** - 自动保存和加载配置
- ✅ **模块化架构** - 代码结构清晰，易于维护

## 代码架构

### 模块化设计
- **renderer/**: 渲染进程相关文件，包含主界面和倒计时界面
- **components/**: UI 组件，负责界面管理和模态框操作
- **services/**: 业务逻辑服务，处理计划的增删改查和与主进程通信
- **utils/**: 工具函数，提供时间格式化等通用功能
- **config/**: 配置管理，统一管理常量和消息文本
- **styles/**: 样式文件，集中管理界面样式

### 架构优势
- **职责分离**: 每个模块都有明确的职责
- **易于测试**: 模块化后便于编写单元测试
- **便于维护**: 修改功能只需关注对应模块
- **可扩展性**: 新功能可以独立添加模块
- **代码复用**: 工具函数可在多处使用

## 配置持久化功能

### 自动保存
- 添加或删除计划时，配置会自动保存到 `config.json` 文件
- 配置文件保存在程序根目录下
- 包含关机计划和最后更新时间

### 自动加载
- 程序启动时会自动检查并加载 `config.json` 文件
- 如果配置文件存在，会恢复之前保存的所有关机计划
- 如果有计划存在，会自动启动定时器开始监听
- 前端界面会显示已保存的配置

### 配置文件格式

```json
{
  "shutdownSchedules": [
    {
      "time": "22-30-00",
      "days": [1, 2, 3, 4, 5]
    },
    {
      "time": "23-00-00", 
      "days": [6, 7]
    }
  ],
  "lastUpdated": "2025-06-25T10:30:00.000Z"
}
```

- `time`: 关机时间，格式为 "HH-MM-SS"
- `days`: 星期几数组，1-7 分别代表周一到周日
- `lastUpdated`: 最后更新时间

## 安装和运行

1. 安装依赖：
```bash
npm install
```

2. 运行程序：
```bash
npm start
```

## 使用说明

1. 点击"添加新计划"按钮
2. 选择关机时间（时:分:秒）
3. 选择要执行的星期
4. 点击"确认添加"保存计划
5. 程序会在后台运行，到达设定时间时自动关机

## 技术实现

- **ES6 模块**: 使用现代化的模块导入/导出语法
- **模块化架构**: 代码按功能分离到不同模块
- **IPC 通信**: 主进程和渲染进程间的数据传递
- **配置持久化**: 使用 Node.js `fs` 模块进行文件读写
- **Bootstrap UI**: 现代化的响应式界面设计

## 注意事项

- 程序需要管理员权限才能执行关机操作
- 关机前会有 60 秒倒计时窗口
- 可以通过系统托盘图标退出程序
- 配置文件位于程序根目录，请勿手动删除
- 如果配置文件损坏，程序会使用默认配置
