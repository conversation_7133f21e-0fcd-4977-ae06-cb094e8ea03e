<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统关机倒计时</title>
    <link rel="stylesheet" href="countdown.css">
</head>
<body>
    <div class="countdown-container">
        <!-- 背景动画 -->
        <div class="background-animation">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 系统图标 -->
            <div class="system-icon">
                <div class="power-icon">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2V6M18.36 6.64L15.54 9.46M22 12H18M18.36 17.36L15.54 14.54M12 18V22M6.64 17.36L9.46 14.54M2 12H6M6.64 6.64L9.46 9.46" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>

            <!-- 倒计时显示 -->
            <div class="countdown-display">
                <div class="countdown-title">系统将在以下时间后关机</div>
                <div class="countdown-time" id="countdownTime">05:00</div>
                <div class="countdown-subtitle">请保存您的工作</div>
            </div>

            <!-- 进度环 -->
            <div class="progress-ring">
                <svg class="progress-ring-svg" width="200" height="200">
                    <circle class="progress-ring-background" cx="100" cy="100" r="90"/>
                    <circle class="progress-ring-progress" cx="100" cy="100" r="90" id="progressCircle"/>
                </svg>
                <div class="progress-percentage" id="progressPercentage">100%</div>
            </div>
        </div>

        <!-- 滑动解锁区域 -->
        <div class="slide-to-cancel">
            <div class="slide-track">
                <div class="slide-track-background"></div>
                <div class="slide-track-progress" id="slideProgress"></div>
                <div class="slide-text" id="slideText">向右滑动取消关机</div>
                <div class="slide-button" id="slideButton">
                    <div class="slide-button-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="slide-button-ripple"></div>
                </div>
            </div>
            <div class="slide-hint">向右滑动并保持1秒以取消关机</div>
        </div>

        <!-- 状态提示 -->
        <div class="status-message" id="statusMessage"></div>
    </div>

    <!-- 大屏幕保持提示 -->
    <div class="hold-prompt" id="holdPrompt">
        <div class="hold-prompt-content">
            <div class="hold-prompt-title">保持滑动位置</div>
            <div class="hold-prompt-subtitle">继续保持1秒以取消关机</div>
            <div class="hold-prompt-timer" id="holdTimer">1.0</div>
            <div class="hold-prompt-progress">
                <div class="hold-prompt-progress-bar" id="holdProgressBar"></div>
            </div>
        </div>
    </div>

    <script src="countdown.js"></script>
</body>
</html>